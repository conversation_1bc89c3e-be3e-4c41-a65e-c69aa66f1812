# SpeakMCP Backend Environment Variables
# Copy this file to .env.local and fill in your actual values

# JWT Secret - Generate a secure random string
JWT_SECRET=your-secure-jwt-secret-here

# Google OAuth Configuration
# Get these from Google Cloud Console > APIs & Services > Credentials
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret

# Groq API Key
# Get this from https://console.groq.com/keys
GROQ_API_KEY=your-groq-api-key

# Allowed Origins (for CORS)
ALLOWED_ORIGINS=*
