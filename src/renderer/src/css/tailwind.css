@tailwind base;
@tailwind components;
@tailwind utilities;
@tailwind variants;

:root {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 0 0% 9%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 96.1%;
  --secondary-foreground: 0 0% 9%;
  --muted: 0 0% 96.1%;
  --muted-foreground: 0 0% 45.1%;
  --accent: 0 0% 96.1%;
  --accent-foreground: 0 0% 9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 95%;
  --input: 0 0% 89.8%;
  --ring: 217 91% 60%;
  --chart-1: 12 76% 61%;
  --chart-2: 173 58% 39%;
  --chart-3: 197 37% 24%;
  --chart-4: 43 74% 66%;
  --chart-5: 27 87% 67%;
  --radius: 0.5rem;
}

.dark {
  --background: 0 0% 3.9%;
  --foreground: 0 0% 98%;
  --card: 0 0% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 0 0% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 0 0% 98%;
  --primary-foreground: 0 0% 9%;
  --secondary: 0 0% 14.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 0 0% 14.9%;
  --muted-foreground: 0 0% 63.9%;
  --accent: 0 0% 14.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 0 0% 14.9%;
  --input: 0 0% 14.9%;
  --ring: 221 83% 53%;
  --chart-1: 220 70% 50%;
  --chart-2: 160 60% 45%;
  --chart-3: 30 80% 55%;
  --chart-4: 280 65% 60%;
  --chart-5: 340 75% 55%;
}

* {
  @apply select-none border-border;
}
body {
  @apply text-foreground antialiased;
}
html {
  color-scheme: light;
}

html.dark {
  color-scheme: dark;
}

html,
body {
  @apply bg-transparent;
}

.app-drag-region {
  -webkit-app-region: drag;
}

.app-no-drag-region {
  -webkit-app-region: no-drag;
}

button,
a[role="button"] {
  @apply cursor-default;
}

button,
a,
input,
textarea,
select {
  -webkit-app-region: no-drag;
}

/* Liquid Glass Effects - Apple 2025 Design Language */
.liquid-glass {
  position: relative;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(2px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.8);
  border-radius: 2rem;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2),
              inset 0 4px 20px rgba(255, 255, 255, 0.3);
}

.liquid-glass::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: inherit;
  backdrop-filter: blur(1px);
  box-shadow: inset -10px -8px 0px -11px rgba(255, 255, 255, 1),
              inset 0px -9px 0px -8px rgba(255, 255, 255, 1);
  opacity: 0.6;
  z-index: -1;
  filter: blur(1px) drop-shadow(10px 4px 6px rgba(0, 0, 0, 0.1)) brightness(115%);
}

.liquid-glass-dark {
  background: rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3),
              inset 0 4px 20px rgba(255, 255, 255, 0.1);
}

.liquid-glass-dark::after {
  background: rgba(255, 255, 255, 0.05);
  box-shadow: inset -10px -8px 0px -11px rgba(255, 255, 255, 0.3),
              inset 0px -9px 0px -8px rgba(255, 255, 255, 0.3);
  filter: blur(1px) drop-shadow(10px 4px 6px rgba(0, 0, 0, 0.3)) brightness(110%);
}

/* Floating waveform container */
.waveform-glass {
  position: fixed;
  background: rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(3px) saturate(150%);
  border: 1px solid rgba(255, 255, 255, 0.6);
  border-radius: 1.5rem;
  box-shadow: 0 12px 40px rgba(31, 38, 135, 0.15),
              inset 0 2px 16px rgba(255, 255, 255, 0.25);
  filter: url(#liquid-glass-distortion);
}

.waveform-glass::before {
  content: '';
  position: absolute;
  inset: 0;
  z-index: 0;
  overflow: hidden;
  border-radius: inherit;
  box-shadow: inset 2px 2px 0px -2px rgba(255, 255, 255, 0.7),
              inset 0 0 3px 1px rgba(255, 255, 255, 0.7);
}

.waveform-glass-dark {
  background: rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25),
              inset 0 2px 16px rgba(255, 255, 255, 0.08);
}

.waveform-glass-dark::before {
  box-shadow: inset 2px 2px 0px -2px rgba(255, 255, 255, 0.2),
              inset 0 0 3px 1px rgba(255, 255, 255, 0.2);
}

/* Waveform bars with liquid glass effect */
.waveform-bar {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.8) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0.6) 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1),
              inset 0 1px 2px rgba(255, 255, 255, 0.8);
  border-radius: 0.25rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.waveform-bar-dark {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.6) 50%,
    rgba(255, 255, 255, 0.8) 100%);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3),
              inset 0 1px 2px rgba(255, 255, 255, 0.4);
}

.waveform-bar-inactive {
  background: rgba(128, 128, 128, 0.3);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05),
              inset 0 1px 1px rgba(255, 255, 255, 0.2);
}

.waveform-bar-inactive-dark {
  background: rgba(128, 128, 128, 0.2);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2),
              inset 0 1px 1px rgba(255, 255, 255, 0.1);
}
